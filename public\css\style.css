/* 班级成绩管理系统样式 */

/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
}

body {
    background-color: #f0f4f8;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(74, 144, 226, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(103, 184, 255, 0.05) 0%, transparent 50%);
    color: #333;
    line-height: 1.6;
    position: relative;
    overflow-x: hidden;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

body::before {
    content: "";
    position: fixed;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: repeating-linear-gradient(
        0deg,
        rgba(120, 120, 120, 0.03) 0px,
        rgba(120, 120, 120, 0.03) 1px,
        transparent 1px,
        transparent 50px
    ),
    repeating-linear-gradient(
        90deg,
        rgba(120, 120, 120, 0.03) 0px,
        rgba(120, 120, 120, 0.03) 1px,
        transparent 1px,
        transparent 50px
    );
    z-index: -1;
}

.container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    width: 100%;
    padding: 20px;
    box-sizing: border-box;
}

button {
    cursor: pointer;
    transition: all 0.3s ease;
}

input, select {
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    outline: none;
}

input:focus, select:focus {
    border-color: #4a90e2;
    box-shadow: 0 0 5px rgba(74, 144, 226, 0.3);
}

/* 登录界面样式 */
#login-container, #module-selector {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 900;
}

.login-box, .selector-box {
    position: relative;
    width: 90%;
    max-width: 400px;
}

.login-box {
    max-width: 400px;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 12px 36px rgba(31, 38, 135, 0.2);
    padding: 45px 40px;
    border-radius: 20px;
    text-align: center;
    transform: translateY(-5vh);
}

.login-box h1 {
    color: #333;
    margin-bottom: 35px;
    font-size: 30px;
    font-weight: 600;
    position: relative;
}

.login-box h1::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, #4a90e2, #67b8ff);
    border-radius: 3px;
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-top: 30px;
}

.login-form input {
    background: rgba(255, 255, 255, 0.8);
    border: none;
    border-radius: 12px;
    padding: 16px;
    font-size: 16px;
    transition: all 0.3s;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.login-form input:focus {
    background: white;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.3);
    transform: translateY(-2px);
}

#login-btn {
    position: relative;
    overflow: hidden;
    z-index: 1;
    background: linear-gradient(90deg, #4a90e2, #67b8ff);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 16px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    margin-top: 10px;
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
}

#login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.6s;
    z-index: -1;
}

#login-btn:hover::before {
    left: 100%;
}

/* 注册和重置密码按钮样式 */
#register-btn, #reset-request-btn, #reset-login-btn {
    position: relative;
    overflow: hidden;
    z-index: 1;
    background: linear-gradient(90deg, #4a90e2, #67b8ff);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 16px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    margin-top: 10px;
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
}

#register-btn::before, #reset-request-btn::before, #reset-login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.6s;
    z-index: -1;
}

#register-btn:hover::before, #reset-request-btn:hover::before, #reset-login-btn:hover::before {
    left: 100%;
}

/* 复制密码按钮悬停效果 */
#copy-password-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

/* 输入框样式已在 .login-form input 中定义，无需重复 */

/* 功能选择界面样式 */
.selector-box {
    max-width: 600px;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 12px 36px rgba(31, 38, 135, 0.2);
    border-radius: 20px;
    padding: 45px 40px;
    text-align: center;
    transform: translateY(-5vh);
}

.selector-box h1 {
    color: #333;
    margin-bottom: 20px;
    font-size: 30px;
    font-weight: 600;
    position: relative;
}

.selector-box h1::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, #4a90e2, #67b8ff);
    border-radius: 3px;
}

.selector-box p {
    margin-bottom: 30px;
    color: #333;
    font-size: 16px;
}

.module-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
}

.module-btn {
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.5);
    border-radius: 16px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
    transition: all 0.3s;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 150px;
    height: 150px;
}

.module-btn:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 15px 30px rgba(74, 144, 226, 0.2);
    background: rgba(255, 255, 255, 0.9);
}

.module-btn .icon {
    font-size: 50px;
    margin-bottom: 15px;
}

.module-btn .text {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.logout-btn {
    background-color: #f44336;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
}

.logout-btn:hover {
    background-color: #d32f2f;
}

/* 模块容器样式 */
#module-container {
    display: flex;
    width: 100%;
    flex-direction: column;
    align-items: center;
    padding-top: 20px;
    padding-bottom: 40px;
    min-height: 100vh;
}

#module-container header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto 20px auto;
}

#module-title {
    font-size: 20px;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 10px;
}

.header-actions button {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
}

#back-btn {
    background-color: #e0e0e0;
    color: #333;
}

#back-btn:hover {
    background-color: #d0d0d0;
}

#logout-btn2 {
    background-color: #f44336;
    color: white;
}

#logout-btn2:hover {
    background-color: #d32f2f;
}

/* 两栏布局样式 */
.two-column-layout {
    display: flex;
    gap: 20px;
    width: 100%;
}

.left-column {
    flex: 1;
    min-width: 0; /* 防止内容溢出 */
}

.right-column {
    flex: 1;
    min-width: 0; /* 防止内容溢出 */
}

/* 模块内容样式 */
.module-content {
    width: 100%;
    margin: 0 auto;
    max-width: 1200px;
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    padding: 25px;
}

.panel {
    width: 100%;
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    padding: 25px;
    margin-bottom: 20px;
}

.panel-title {
    color: #333;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(74, 144, 226, 0.2);
    position: relative;
}

.panel-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, #4a90e2, #67b8ff);
}

/* 操作栏样式 */
.actions-bar {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}

.filters {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.filter-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-item label {
    font-size: 14px;
    color: #666;
}

/* 按钮区域样式 */
.buttons {
    display: flex;
    justify-content: space-between;
    gap: 15px;
    margin-top: 15px;
}

.buttons button {
    padding: 10px 20px;
    background: linear-gradient(90deg, #4a90e2, #67b8ff);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    flex: 1;
    text-align: center;
    box-shadow: 0 3px 6px rgba(74, 144, 226, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.buttons button:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.6s;
}

.buttons button:hover {
    background: linear-gradient(90deg, #3a7bc8, #4a90e2);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(74, 144, 226, 0.3);
}

.buttons button:hover:before {
    left: 100%;
}

.buttons button:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(74, 144, 226, 0.2);
}

/* 数据表格样式 */
.data-grid {
    overflow-x: auto;
    max-height: 500px;
    overflow-y: auto;
    margin-bottom: 20px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.data-table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.data-table th {
    background: linear-gradient(90deg, rgba(74, 144, 226, 0.1), rgba(103, 184, 255, 0.1));
    color: #333;
    font-weight: 600;
    text-align: left;
    padding: 15px;
}

.data-table td {
    padding: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.data-table tr:last-child td {
    border-bottom: none;
}

.data-table tbody tr {
    transition: all 0.2s;
}

.data-table tbody tr:hover {
    background-color: rgba(74, 144, 226, 0.05);
}

/* 操作按钮样式 */
.action-btn {
    padding: 5px 10px;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    margin-right: 5px;
}

.increase-btn {
    background-color: #4caf50;
    color: white;
}

.increase-btn:hover {
    background-color: #3d8b40;
}

.decrease-btn {
    background-color: #f44336;
    color: white;
}

.decrease-btn:hover {
    background-color: #d32f2f;
}

/* 排行榜样式 */
.rankings {
    height: 100%;
    padding: 15px;
}

.ranking-box {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    padding: 25px;
    height: 100%;
}

.ranking-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e1e1e1;
}

.ranking-header h3 {
    font-size: 18px;
    color: #333;
    margin: 0;
}

.ranking-type {
    display: flex;
    align-items: center;
    gap: 10px;
}

.ranking-type label {
    font-size: 14px;
    color: #666;
}

.ranking-type select {
    padding: 5px 10px;
    font-size: 14px;
}

/* 排行榜分区 */
.ranking-section {
    margin-bottom: 20px;
    padding-bottom: 15px;
}

.ranking-section h4 {
    font-size: 16px;
    color: #333;
    margin: 15px 0 10px;
    padding-bottom: 5px;
    border-bottom: 1px dashed #ddd;
}

.ranking-section.top-classes {
    margin-top: 25px;
    padding-top: 5px;
    border-top: 1px solid #e1e1e1;
}

.ranking-item {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    border-bottom: 1px solid #e1e1e1;
    align-items: center;
}

.ranking-item:last-child {
    border-bottom: none;
}

.ranking-item:hover {
    background-color: #f5f5f5;
}

/* 排名标记样式 */
.rank {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #e8e8e8;
    color: #555;
    margin-right: 10px;
    font-weight: bold;
    font-size: 14px;
}

/* 金银铜牌样式 */
.rank-1 .rank:before {
    content: "🥇";
    font-size: 18px;
}

.rank-2 .rank:before {
    content: "🥈";
    font-size: 18px;
}

.rank-3 .rank:before {
    content: "🥉";
    font-size: 18px;
}

.rank-1 .rank, .rank-2 .rank, .rank-3 .rank {
    background: transparent;
    color: transparent;
    position: relative;
}

.rank-1 .rank:before, .rank-2 .rank:before, .rank-3 .rank:before {
    position: absolute;
    color: initial;
}

/* 图表容器样式 */
.chart-container {
    height: 300px;
    margin-top: 20px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 无数据提示样式 */
.no-data {
    text-align: center;
    padding: 20px;
    color: #999;
    font-style: italic;
}

/* 重置按钮样式 */
.reset-container {
    position: fixed;
    bottom: 20px;
    left: 0;
    right: 0;
    text-align: center;
    z-index: 100;
}

#reset-data {
    background-color: #ff9800;
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

#reset-data:hover {
    background-color: #f57c00;
}

/* 加载指示器 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #fff;
    font-size: 16px;
}

/* 响应式设计 */
@media screen and (max-width: 968px) {
    .two-column-layout {
        flex-direction: column;
    }
    
    .module-buttons {
        flex-direction: column;
        gap: 20px;
    }
    
    .batch-actions {
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .filters {
        flex-direction: column;
    }
    
    .ranking-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .data-table th, .data-table td {
        padding: 10px 8px;
        font-size: 14px;
    }
    
    .login-box, .selector-box {
        padding: 20px;
    }
    
    .panel {
        padding: 15px;
    }
}

/* 批量操作区域样式 */
.batch-actions {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    margin-bottom: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.batch-btn {
    margin-right: 10px;
    padding: 8px 15px;
    background: rgba(255, 255, 255, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.batch-btn:hover {
    background: rgba(255, 255, 255, 0.7);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

#batch-increase-medal, #batch-increase-typing {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.5), rgba(39, 174, 96, 0.5));
    border-color: rgba(46, 204, 113, 0.3);
    color: #fff;
    font-weight: 600;
}

#batch-increase-medal:hover, #batch-increase-typing:hover {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.7), rgba(39, 174, 96, 0.7));
    box-shadow: 0 4px 12px rgba(46, 204, 113, 0.3);
}

#batch-decrease-medal, #batch-decrease-typing {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.5), rgba(192, 57, 43, 0.5));
    border-color: rgba(231, 76, 60, 0.3);
    color: #fff;
    font-weight: 600;
}

#batch-decrease-medal:hover, #batch-decrease-typing:hover {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.7), rgba(192, 57, 43, 0.7));
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.medal-count-input, .speed-count-input {
    width: 60px;
    padding: 8px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 复选框样式 */
input[type="checkbox"] {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

/* 操作按钮样式 */
.action-btn {
    width: 24px;
    height: 24px;
    margin: 0 2px;
    border-radius: 50%;
    border: 1px solid #ddd;
    background-color: white;
    cursor: pointer;
    font-weight: bold;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.increase-btn {
    color: #2e7d32;
    border-color: #a5d6a7;
}

.increase-btn:hover {
    background-color: #e8f5e9;
}

.decrease-btn {
    color: #c62828;
    border-color: #ef9a9a;
}

.decrease-btn:hover {
    background-color: #ffebee;
}

/* 消息提示框 */
#message-box {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 25px;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    color: #333;
    font-weight: 600;
    opacity: 0;
    transform: translateY(-20px);
    transition: opacity 0.3s, transform 0.3s;
    z-index: 9998;
}

#message-box.show {
    opacity: 1;
    transform: translateY(0);
}

/* 答案显示样式 */
.answer-reveal {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-top: 15px;
    border-left: 5px solid #28a745;
    transition: all 0.3s ease;
    animation: fadeIn 0.5s ease forwards;
}

.answer-reveal.error-message {
    border-left: 5px solid #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 确认对话框 */
.dialog-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9997;
}

/* 改进的对话框样式 */
.dialog {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    padding: 30px;
    max-width: 450px;
    width: 90%;
    animation: dialogFadeIn 0.3s ease forwards;
}

@keyframes dialogFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.dialog-message {
    margin-bottom: 25px;
    font-size: 18px;
    font-weight: 500;
    text-align: center;
    color: #333;
    line-height: 1.6;
}

.dialog-message strong {
    color: #dc3545;
}

.dialog-title {
    font-size: 22px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
    text-align: center;
    position: relative;
}

.dialog-title:after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, #4a90e2, #67b8ff);
    border-radius: 3px;
}

.dialog-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.dialog-button {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s;
}

.dialog-button.confirm {
    background: linear-gradient(135deg, #4a90e2, #67b8ff);
    color: white;
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
}

.dialog-button.confirm:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
}

.dialog-button.cancel {
    background: rgba(255, 255, 255, 0.3);
    color: #333;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.dialog-button.cancel:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 毛玻璃效果和现代UI样式 */
.glass-effect {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 按钮样式 */
.template-btn {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    font-weight: 600;
    box-shadow: 0 4px 10px rgba(52, 152, 219, 0.2);
    transition: all 0.3s;
}

.template-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(52, 152, 219, 0.3);
}

/* 动态背景样式 */
.animated-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
    overflow: hidden;
}

.shape {
    position: absolute;
    border-radius: 50%;
    filter: blur(40px);
    opacity: 0.3;
}

.shape1 {
    background: linear-gradient(135deg, #4a90e2, #67b8ff);
    width: 400px;
    height: 400px;
    top: -100px;
    left: -100px;
    animation: float 15s infinite alternate ease-in-out;
}

.shape2 {
    background: linear-gradient(135deg, #3498db, #2980b9);
    width: 300px;
    height: 300px;
    top: 60%;
    right: -50px;
    animation: float 20s infinite alternate-reverse ease-in-out;
}

.shape3 {
    background: linear-gradient(135deg, #67b8ff, #4a90e2);
    width: 200px;
    height: 200px;
    bottom: 10%;
    left: 10%;
    animation: float 17s infinite alternate ease-in-out;
}

.shape4 {
    background: linear-gradient(135deg, #2980b9, #3498db);
    width: 250px;
    height: 250px;
    top: 20%;
    right: 30%;
    animation: float 12s infinite alternate-reverse ease-in-out;
}

@keyframes float {
    0% {
        transform: translate(0, 0);
    }
    50% {
        transform: translate(30px, 20px);
    }
    100% {
        transform: translate(-30px, -20px);
    }
}

.class-selector, .grade-selector {
    margin-bottom: 15px;
    background: rgba(255, 255, 255, 0.2);
    padding: 10px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.class-selector label, .grade-selector label {
    margin-right: 10px;
    font-weight: 500;
}

.class-selector select, .grade-selector select {
    padding: 5px 10px;
    border-radius: 5px;
    border: 1px solid rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.7);
    font-size: 14px;
}

/* 选择器悬停效果 */
.class-selector select:hover, .grade-selector select:hover {
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 0 8px rgba(74, 144, 226, 0.3);
}

.ranking-section {
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.ranking-section h4 {
    margin: 0;
    padding: 10px;
    background: rgba(74, 144, 226, 0.2);
    text-align: center;
    font-size: 16px;
    font-weight: 600;
}

/* 进步之星样式 */
.positive-progress {
    color: #28a745;
    font-weight: bold;
}

.negative-progress {
    color: #dc3545;
    font-weight: bold;
}

/* 添加打字问题相关的样式 */
.question-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
}

.question-container header {
    background-color: #f8f9fa;
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.question-container header h1 {
    margin: 0;
    font-size: 24px;
    color: #2c3e50;
}

.question-box {
    width: 95%;
    max-width: 900px;
    margin: 20px auto;
    padding: 30px;
    border-radius: 10px;
}

.question-container .question-text {
    font-size: 26px;
    color: #2c3e50;
    text-align: center;
    margin: 25px 0;
    font-weight: bold;
    position: relative;
    background-color: rgba(255, 255, 255, 0.7);
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.question-container .question-text::after {
    content: '?';
    position: absolute;
    right: 20px;
    bottom: 20px;
    font-size: 40px;
    color: #4a90e2;
    opacity: 0.3;
}

.question-progress {
    margin: 30px 0;
}

.progress-text {
    text-align: center;
    margin-bottom: 10px;
    color: #6c757d;
}

.progress-bar {
    height: 10px;
    background-color: #e9ecef;
    border-radius: 5px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: #4a90e2;
    border-radius: 5px;
    transition: width 0.3s ease;
}

.instruction-text {
    text-align: center;
    margin: 30px 0;
    font-size: 18px;
    color: #6c757d;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
}

/* 尝试进度条样式 */
.attempts-container {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.attempts-label {
    font-size: 16px;
    color: #495057;
    margin-bottom: 8px;
    text-align: center;
}

.attempts-progress-bar {
    height: 10px;
    background-color: #e9ecef;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.attempts-progress-fill {
    height: 100%;
    background-color: #4a90e2;
    border-radius: 5px;
    transition: width 0.3s ease, background-color 0.3s ease;
    width: 0%;
}

/* 按键反馈动画 */
.key-pressed {
    animation: keyFeedback 0.3s ease;
}

@keyframes keyFeedback {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
        background-color: #e9f5ff;
    }
    100% {
        transform: scale(1);
    }
}

/* 修改跳过按钮样式，使其与布局匹配 */
#skip-questions {
    padding: 8px 16px;
    background-color: #6c757d;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 14px;
}

#skip-questions:hover {
    background-color: #5a6268;
}

.question-container:focus {
    outline: none;
}

/* 下拉式文章选择样式 */
.article-selection-controls {
    margin: 15px 0;
}

.select-group {
    width: 100%;
}

.article-select {
    width: 100%;
    height: 40px;
    padding: 8px 15px;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    background-color: rgba(255, 255, 255, 0.8);
    color: #333;
    font-size: 15px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg>');
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 16px;
    cursor: pointer;
    transition: all 0.3s;
    max-width: 100%;
    min-width: 250px; /* 确保最小宽度 */
}

.article-select:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
}

/* 选项组样式 */
.article-select optgroup {
    font-weight: 600;
    background-color: rgba(74, 144, 226, 0.05);
    padding: 5px 0;
}

.article-select option {
    padding: 8px;
    background-color: white;
    font-weight: normal;
}

/* 打字区域主体 */
.typing-area {
    flex: 1;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    overflow-y: auto;
    height: auto;
    display: flex;
    flex-direction: column;
    position: relative; /* 用于统计面板的定位参考 */
}

/* 顶部控制栏 */
.typing-area-header {
    padding: 10px;
    margin: 10px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* 左侧语言和文章选择 */
.language-article-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 60%;
}

/* 语言选择按钮组 */
.language-switch {
    display: flex;
    gap: 5px;
}

.language-switch .lang-btn {
    padding: 8px 15px;
    border: none;
    background-color: rgba(255, 255, 255, 0.5);
    color: #555;
    cursor: pointer;
    transition: all 0.3s;
    font-weight: 500;
    border-radius: 6px;
}

.language-switch .lang-btn.active {
    background-color: #4a90e2;
    color: white;
    box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

/* 文章选择下拉框样式改进 */
.article-selection-controls {
    flex: 1; /* 占据可用空间 */
    margin-right: 15px;
    max-width: 50%; /* 限制最大宽度为50% */
}

.select-group {
    width: 100%; /* 宽度调整 */
    max-width: 300px; /* 最大宽度限制 */
}

/* 右侧统计面板 - 调整为内联显示 */
.statistics-panel {
    position: static;
    display: flex;
    gap: 15px; /* 增加间距 */
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 8px 15px; /* 调整内边距 */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    flex: 0 1 auto; /* 根据内容自动调整宽度 */
}

/* 统计项目样式优化 */
.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 3px 8px; /* 调整内边距 */
    border-radius: 6px;
    background-color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s;
    min-width: 80px; /* 设置最小宽度 */
}

.stat-label {
    font-size: 12px; /* 增大字体 */
    color: #666;
    margin-bottom: 2px;
    white-space: nowrap;
}

.stat-value {
    font-size: 15px; /* 增大字体 */
    font-weight: 600;
    color: #333;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
    .typing-area-header {
        flex-direction: row; /* 保持水平布局 */
        flex-wrap: wrap; /* 允许折行 */
        gap: 15px;
        justify-content: space-between;
    }
    
    .article-selection-controls {
        flex: 1 1 100%; /* 在小屏幕上文章选择占满一行 */
        margin-right: 0;
        margin-bottom: 10px;
        max-width: 100%; /* 在响应式布局下允许全宽 */
    }
    
    .statistics-panel {
        flex: 1 1 100%; /* 在小屏幕上统计面板占满一行 */
        justify-content: space-between;
        padding: 10px;
    }
    
    .select-group {
        width: 100%;
        max-width: none;
    }
    
    .article-select {
        min-width: unset;
    }
    
    .stat-item {
        min-width: 70px;
        padding: 3px 5px;
    }
}

/* 文章弹窗样式 */
#article-content {
    width: 100%;
    resize: vertical;
    min-height: 120px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: inherit;
}

.required {
    color: #e74c3c;
    margin-left: 3px;
}

/* modal按钮相关样式补充 */
.modal-btn:hover {
    opacity: 0.9;
}

.modal-btn-primary:hover {
    background-color: #3982d7;
}

.modal-btn-secondary:hover {
    background-color: #d4d4d4;
} 