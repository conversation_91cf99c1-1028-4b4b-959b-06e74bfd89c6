<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教师注册 - 班级成绩管理系统</title>
    <link rel="stylesheet" href="/css/style.css">
    <style>
        /* 确保页面全屏显示，不叠加 */
        body {
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #f0f4f8;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(74, 144, 226, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(103, 184, 255, 0.05) 0%, transparent 50%);
        }
        
        /* 注册容器样式 */
        .register-container {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body>
    <!-- 教师注册界面 -->
    <div class="register-container">
        <div class="login-box glass-effect">
            <h1>教师注册</h1>
            <div class="register-form">
                <input type="text" id="reg-username" placeholder="用户名 (3-20个字符)" required>
                <input type="password" id="reg-password" placeholder="密码 (至少8位，包含字母和数字)" required>
                <input type="password" id="reg-confirm-password" placeholder="确认密码" required>
                <button id="register-btn" class="login-btn">注册</button>
                <div class="login-link">
                    <p>已有账户？<a href="/">返回登录</a></p>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载脚本 -->
    <script src="/js/config.js"></script>
    <script src="/js/utils.js"></script>
    <script>
        // 教师注册功能
        document.addEventListener('DOMContentLoaded', function() {
            const registerBtn = document.getElementById('register-btn');
            if (registerBtn) {
                registerBtn.addEventListener('click', handleRegister);
            }
        });

        /**
         * 处理注册
         */
        async function handleRegister() {
            const username = document.getElementById('reg-username').value.trim();
            const password = document.getElementById('reg-password').value;
            const confirmPassword = document.getElementById('reg-confirm-password').value;

            // 验证输入
            if (!username || !password || !confirmPassword) {
                showMessage('请填写所有字段', 'error');
                return;
            }

            if (username.length < 3 || username.length > 20) {
                showMessage('用户名长度应在3-20个字符之间', 'error');
                return;
            }

            if (password.length < 8) {
                showMessage('密码长度至少8位', 'error');
                return;
            }

            if (!/(?=.*[a-zA-Z])(?=.*\d)/.test(password)) {
                showMessage('密码必须包含字母和数字', 'error');
                return;
            }

            if (password !== confirmPassword) {
                showMessage('两次输入的密码不一致', 'error');
                return;
            }

            try {
                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username,
                        password,
                        role: 'teacher'
                    })
                });

                const result = await response.json();

                if (response.ok) {
                    showMessage('注册成功！请返回登录', 'success');
                    // 清空表单
                    document.getElementById('reg-username').value = '';
                    document.getElementById('reg-password').value = '';
                    document.getElementById('reg-confirm-password').value = '';
                    
                    // 3秒后跳转到登录页面
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 3000);
                } else {
                    showMessage(result.error || '注册失败', 'error');
                }
            } catch (error) {
                console.error('注册错误:', error);
                showMessage('注册失败，请重试', 'error');
            }
        }

        /**
         * 显示消息
         */
        function showMessage(message, type = 'info') {
            // 移除现有消息
            const existingMessage = document.querySelector('.message');
            if (existingMessage) {
                existingMessage.remove();
            }

            // 创建新消息
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = message;
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 6px;
                color: white;
                font-weight: 500;
                z-index: 1000;
                animation: slideIn 0.3s ease;
                background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
            `;

            document.body.appendChild(messageDiv);

            // 3秒后自动移除
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>
