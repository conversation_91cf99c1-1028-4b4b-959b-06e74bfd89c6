<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重置密码 - 班级成绩管理系统</title>
    <link rel="stylesheet" href="/css/style.css">
    <style>
        /* 确保页面全屏显示，不叠加 */
        body {
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #f0f4f8;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(74, 144, 226, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(103, 184, 255, 0.05) 0%, transparent 50%);
        }
        
        /* 重置密码容器样式 */
        .reset-container {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
        }
        
        /* 复选框样式 */
        .checkbox-container {
            margin: 15px 0;
            padding: 10px;
            background: rgba(74, 144, 226, 0.1);
            border-radius: 6px;
            border: 1px solid rgba(74, 144, 226, 0.3);
        }
        
        .checkbox-label {
            color: #333;
            font-size: 14px;
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        
        .checkbox-label input[type="checkbox"] {
            margin-right: 8px;
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <!-- 重置密码界面 -->
    <div class="reset-container">
        <div class="login-box glass-effect">
            <h1>重置密码</h1>
            <div class="reset-password-form">
                <div id="reset-step-1">
                    <input type="text" id="reset-username" placeholder="请输入用户名" required>
                    <input type="password" id="reset-new-password" placeholder="请输入新密码（至少6位）" required>
                    <input type="password" id="reset-confirm-password" placeholder="请确认新密码" required>
                    <div class="checkbox-container">
                        <label class="checkbox-label">
                            <input type="checkbox" id="generate-random-password">
                            <span>自动生成随机密码（8位字母数字组合）</span>
                        </label>
                    </div>
                    <button id="reset-request-btn" class="login-btn">重置密码</button>
                    <div class="login-link">
                        <p><a href="/">返回登录</a></p>
                    </div>
                </div>
                <div id="reset-step-2" style="display: none;">
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #e9ecef;">
                        <h3 style="color: #28a745; margin-bottom: 15px; text-align: center;">
                            <i class="fas fa-check-circle"></i> 密码重置成功
                        </h3>
                        <p style="color: #333; margin-bottom: 15px; text-align: center;">
                            您的新密码是：
                        </p>
                        <div style="background: #fff; padding: 15px; border-radius: 6px; margin-bottom: 15px; border: 2px solid #28a745; text-align: center;">
                            <span id="new-password-display" style="font-family: 'Courier New', monospace; font-size: 20px; color: #333; font-weight: bold; letter-spacing: 2px;"></span>
                            <br>
                            <button id="copy-password-btn" style="margin-top: 10px; padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
                                <i class="fas fa-copy"></i> 复制密码
                            </button>
                        </div>
                        <div style="background: #fff3cd; padding: 12px; border-radius: 6px; border-left: 4px solid #ffc107;">
                            <p style="color: #856404; font-size: 14px; margin: 0;">
                                <strong>重要提示：</strong> 请妥善保管您的新密码，建议首次登录后立即修改为您熟悉的密码。
                            </p>
                        </div>
                    </div>
                    <button id="reset-login-btn" class="login-btn">返回登录</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载脚本 -->
    <script src="/js/config.js"></script>
    <script src="/js/utils.js"></script>
    <script>
        // 重置密码功能
        document.addEventListener('DOMContentLoaded', function() {
            const resetRequestBtn = document.getElementById('reset-request-btn');
            const resetLoginBtn = document.getElementById('reset-login-btn');
            const copyPasswordBtn = document.getElementById('copy-password-btn');
            const generateRandomCheckbox = document.getElementById('generate-random-password');
            const newPasswordInput = document.getElementById('reset-new-password');
            const confirmPasswordInput = document.getElementById('reset-confirm-password');

            if (resetRequestBtn) {
                resetRequestBtn.addEventListener('click', handleResetPassword);
            }
            
            if (resetLoginBtn) {
                resetLoginBtn.addEventListener('click', () => {
                    window.location.href = '/';
                });
            }
            
            if (copyPasswordBtn) {
                copyPasswordBtn.addEventListener('click', copyPassword);
            }
            
            // 自动生成密码复选框事件
            if (generateRandomCheckbox) {
                generateRandomCheckbox.addEventListener('change', function() {
                    if (this.checked) {
                        newPasswordInput.disabled = true;
                        confirmPasswordInput.disabled = true;
                        newPasswordInput.placeholder = '将自动生成随机密码';
                        confirmPasswordInput.placeholder = '将自动生成随机密码';
                    } else {
                        newPasswordInput.disabled = false;
                        confirmPasswordInput.disabled = false;
                        newPasswordInput.placeholder = '请输入新密码（至少6位）';
                        confirmPasswordInput.placeholder = '请确认新密码';
                    }
                });
            }
        });

        /**
         * 处理重置密码
         */
        async function handleResetPassword() {
            const username = document.getElementById('reset-username').value.trim();
            const generateRandom = document.getElementById('generate-random-password').checked;
            let newPassword = '';

            if (!username) {
                showMessage('请输入用户名', 'error');
                return;
            }

            if (!generateRandom) {
                const password = document.getElementById('reset-new-password').value;
                const confirmPassword = document.getElementById('reset-confirm-password').value;

                if (!password || !confirmPassword) {
                    showMessage('请输入新密码和确认密码', 'error');
                    return;
                }

                if (password.length < 6) {
                    showMessage('密码长度至少6位', 'error');
                    return;
                }

                if (password !== confirmPassword) {
                    showMessage('两次输入的密码不一致', 'error');
                    return;
                }

                newPassword = password;
            } else {
                newPassword = 'auto-generate';
            }

            try {
                const response = await fetch('/api/reset-password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username,
                        newPassword
                    })
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    // 显示新密码
                    document.getElementById('new-password-display').textContent = result.newPassword;
                    document.getElementById('reset-step-1').style.display = 'none';
                    document.getElementById('reset-step-2').style.display = 'block';

                    showMessage('密码重置成功', 'success');
                } else {
                    showMessage(result.message || '重置密码失败', 'error');
                }
            } catch (error) {
                console.error('重置密码错误:', error);
                showMessage('重置密码失败，请重试', 'error');
            }
        }

        /**
         * 复制密码
         */
        function copyPassword() {
            const passwordText = document.getElementById('new-password-display').textContent;
            navigator.clipboard.writeText(passwordText).then(() => {
                showMessage('密码已复制到剪贴板', 'success');
            }).catch(() => {
                showMessage('复制失败，请手动复制', 'error');
            });
        }

        /**
         * 显示消息
         */
        function showMessage(message, type = 'info') {
            // 移除现有消息
            const existingMessage = document.querySelector('.message');
            if (existingMessage) {
                existingMessage.remove();
            }

            // 创建新消息
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = message;
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 6px;
                color: white;
                font-weight: 500;
                z-index: 1000;
                animation: slideIn 0.3s ease;
                background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
            `;

            document.body.appendChild(messageDiv);

            // 3秒后自动移除
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>
